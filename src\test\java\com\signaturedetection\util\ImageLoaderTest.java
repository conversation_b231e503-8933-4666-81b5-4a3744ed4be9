package com.signaturedetection.util;

import nu.pattern.OpenCV;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.opencv.core.Mat;

import static org.junit.jupiter.api.Assertions.*;

class ImageLoaderTest {
    
    private ImageLoader imageLoader;
    
    @BeforeAll
    static void loadOpenCV() {
        try {
            OpenCV.loadShared();
        } catch (UnsatisfiedLinkError | RuntimeException e) {
            OpenCV.loadLocally();
        }
    }
    
    @BeforeEach
    void setUp() {
        imageLoader = new ImageLoader();
    }
    
    @Test
    void testLoadImageWithNonExistentFile() {
        Mat result = imageLoader.loadImage("non_existent_file.tiff");
        
        assertNotNull(result);
        assertTrue(result.empty());
        
        result.release();
    }
    
    @Test
    void testIsSupportedFormat() {
        assertTrue(imageLoader.isSupportedFormat("test.tiff"));
        assertTrue(imageLoader.isSupportedFormat("test.tif"));
        assertTrue(imageLoader.isSupportedFormat("test.png"));
        assertTrue(imageLoader.isSupportedFormat("test.jpg"));
        assertTrue(imageLoader.isSupportedFormat("test.jpeg"));
        assertTrue(imageLoader.isSupportedFormat("test.bmp"));
        
        // Test case insensitive
        assertTrue(imageLoader.isSupportedFormat("TEST.TIFF"));
        assertTrue(imageLoader.isSupportedFormat("Test.Png"));
        
        // Test unsupported formats
        assertFalse(imageLoader.isSupportedFormat("test.gif"));
        assertFalse(imageLoader.isSupportedFormat("test.txt"));
        assertFalse(imageLoader.isSupportedFormat("test"));
    }
    
    @Test
    void testLoadImageWithInvalidPath() {
        Mat result = imageLoader.loadImage("");
        
        assertNotNull(result);
        assertTrue(result.empty());
        
        result.release();
    }
    
    @Test
    void testLoadImageWithNullPath() {
        assertThrows(Exception.class, () -> {
            imageLoader.loadImage(null);
        });
    }
}
