package com.signaturedetection.preprocessing;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Implementation of ImagePreprocessor that removes lines from images before signature detection.
 */
public class LineRemovalPreprocessor implements ImagePreprocessor {
    
    private static final Logger logger = LoggerFactory.getLogger(LineRemovalPreprocessor.class);
    
    // Configuration parameters
    private int horizontalKernelSize = 40;
    private int verticalKernelSize = 40;
    private int morphologyIterations = 2;
    private double binaryThreshold = 127.0;
    
    @Override
    public Mat preprocess(Mat inputImage) {
        logger.info("Starting image preprocessing");
        
        // Convert to grayscale if needed
        Mat grayImage = new Mat();
        if (inputImage.channels() > 1) {
            Imgproc.cvtColor(inputImage, grayImage, Imgproc.COLOR_BGR2GRAY);
        } else {
            grayImage = inputImage.clone();
        }
        
        // Apply denoising
        Mat denoisedImage = denoise(grayImage);
        
        // Binarize the image
        Mat binaryImage = binarize(denoisedImage);
        
        // Remove lines
        Mat processedImage = removeLines(binaryImage);
        
        logger.info("Image preprocessing completed");
        return processedImage;
    }
    
    @Override
    public Mat removeLines(Mat image) {
        logger.debug("Removing horizontal and vertical lines");
        
        Mat result = image.clone();
        
        // Remove horizontal lines
        Mat horizontalKernel = Imgproc.getStructuringElement(
            Imgproc.MORPH_RECT, 
            new Size(horizontalKernelSize, 1)
        );
        Mat horizontalLines = new Mat();
        Imgproc.morphologyEx(result, horizontalLines, Imgproc.MORPH_OPEN, horizontalKernel);
        
        // Remove vertical lines
        Mat verticalKernel = Imgproc.getStructuringElement(
            Imgproc.MORPH_RECT, 
            new Size(1, verticalKernelSize)
        );
        Mat verticalLines = new Mat();
        Imgproc.morphologyEx(result, verticalLines, Imgproc.MORPH_OPEN, verticalKernel);
        
        // Combine detected lines
        Mat detectedLines = new Mat();
        Core.add(horizontalLines, verticalLines, detectedLines);
        
        // Remove lines from original image
        Mat mask = new Mat();
        Core.bitwise_not(detectedLines, mask);
        Core.bitwise_and(result, mask, result);
        
        // Clean up small artifacts
        Mat cleanupKernel = Imgproc.getStructuringElement(Imgproc.MORPH_ELLIPSE, new Size(3, 3));
        Imgproc.morphologyEx(result, result, Imgproc.MORPH_CLOSE, cleanupKernel, new Point(-1, -1), morphologyIterations);
        
        logger.debug("Line removal completed");
        return result;
    }
    
    @Override
    public Mat denoise(Mat image) {
        logger.debug("Applying noise reduction");
        
        Mat denoised = new Mat();
        // Apply Gaussian blur to reduce noise
        Imgproc.GaussianBlur(image, denoised, new Size(3, 3), 0);
        
        // Apply median filter for additional noise reduction
        Mat medianFiltered = new Mat();
        Imgproc.medianBlur(denoised, medianFiltered, 3);
        
        return medianFiltered;
    }
    
    @Override
    public Mat binarize(Mat image) {
        logger.debug("Converting image to binary");
        
        Mat binary = new Mat();
        // Use adaptive threshold for better results with varying lighting
        Imgproc.adaptiveThreshold(
            image, 
            binary, 
            255, 
            Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C, 
            Imgproc.THRESH_BINARY, 
            11, 
            2
        );
        
        return binary;
    }
    
    // Getter and setter methods for configuration
    public void setHorizontalKernelSize(int size) {
        this.horizontalKernelSize = size;
    }
    
    public void setVerticalKernelSize(int size) {
        this.verticalKernelSize = size;
    }
    
    public void setMorphologyIterations(int iterations) {
        this.morphologyIterations = iterations;
    }
    
    public void setBinaryThreshold(double threshold) {
        this.binaryThreshold = threshold;
    }
}
