package com.signaturedetection.preprocessing;

import org.opencv.core.Mat;

/**
 * Interface for image preprocessing operations.
 */
public interface ImagePreprocessor {
    
    /**
     * Preprocesses the input image by removing lines and preparing it for signature detection.
     * 
     * @param inputImage The input image matrix
     * @return The preprocessed image matrix with lines removed
     */
    Mat preprocess(Mat inputImage);
    
    /**
     * Removes horizontal and vertical lines from the image.
     * 
     * @param image The input image matrix
     * @return The image with lines removed
     */
    Mat removeLines(Mat image);
    
    /**
     * Applies noise reduction to the image.
     * 
     * @param image The input image matrix
     * @return The denoised image
     */
    Mat denoise(Mat image);
    
    /**
     * Converts the image to binary (black and white).
     * 
     * @param image The input image matrix
     * @return The binary image
     */
    Mat binarize(Mat image);
}
