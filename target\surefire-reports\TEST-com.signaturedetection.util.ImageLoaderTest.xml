<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.signaturedetection.util.ImageLoaderTest" time="0.007" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\test-classes;C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\classes;C:\Users\<USER>\.m2\repository\org\openpnp\opencv\4.9.0-0\opencv-4.9.0-0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-imaging\1.0.0-alpha5\commons-imaging-1.0.0-alpha5.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-tiff\3.9.4\imageio-tiff-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-core\3.9.4\imageio-core-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-metadata\3.9.4\imageio-metadata-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-lang\3.9.4\common-lang-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-io\3.9.4\common-io-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-image\3.9.4\common-image-3.9.4.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.0\junit-jupiter-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Europe/London"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="GB"/>
    <property name="sun.boot.library.path" value="C:\InfoInputSolution\java\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972\surefirebooter-20250818161228215_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972 2025-08-18T16-12-27_962-jvmRun1 surefire-20250818161228215_1tmp surefire_0-20250818161228215_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\test-classes;C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\classes;C:\Users\<USER>\.m2\repository\org\openpnp\opencv\4.9.0-0\opencv-4.9.0-0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-imaging\1.0.0-alpha5\commons-imaging-1.0.0-alpha5.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-tiff\3.9.4\imageio-tiff-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-core\3.9.4\imageio-core-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-metadata\3.9.4\imageio-metadata-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-lang\3.9.4\common-lang-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-io\3.9.4\common-io-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-image\3.9.4\common-image-3.9.4.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.0\junit-jupiter-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\InfoInputSolution\java"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972\surefirebooter-20250818161228215_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.9+9"/>
    <property name="user.name" value="10187506"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-17.0.9+9"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.9"/>
    <property name="user.dir" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\InfoInputSolution\java\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\InfoInputSolution\java\bin;C:\Program Files\nodejs\;C:\Mavern\mvn\bin\;C:\Program Files\RedHat\Podman\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3\bin;;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.1.1\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="17.0.9+9"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testIsSupportedFormat" classname="com.signaturedetection.util.ImageLoaderTest" time="0.001"/>
  <testcase name="testLoadImageWithNullPath" classname="com.signaturedetection.util.ImageLoaderTest" time="0.001"/>
  <testcase name="testLoadImageWithNonExistentFile" classname="com.signaturedetection.util.ImageLoaderTest" time="0.0">
    <system-out><![CDATA[16:12:29.775 [main] ERROR c.s.util.ImageLoader - Image file does not exist: non_existent_file.tiff
]]></system-out>
  </testcase>
  <testcase name="testLoadImageWithInvalidPath" classname="com.signaturedetection.util.ImageLoaderTest" time="0.0">
    <system-out><![CDATA[16:12:29.776 [main] ERROR c.s.util.ImageLoader - Image file does not exist: 
]]></system-out>
  </testcase>
</testsuite>