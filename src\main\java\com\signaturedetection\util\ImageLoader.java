package com.signaturedetection.util;

import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.File;
import java.io.IOException;

/**
 * Utility class for loading images into OpenCV Mat format.
 */
public class ImageLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageLoader.class);
    
    /**
     * Loads an image from file path into OpenCV Mat format.
     * 
     * @param imagePath Path to the image file
     * @return Mat object containing the image data, or empty Mat if loading fails
     */
    public Mat loadImage(String imagePath) {
        try {
            File imageFile = new File(imagePath);
            if (!imageFile.exists()) {
                logger.error("Image file does not exist: {}", imagePath);
                return new Mat();
            }
            
            // Use ImageIO to read the image (supports TIFF and other formats)
            BufferedImage bufferedImage = ImageIO.read(imageFile);
            if (bufferedImage == null) {
                logger.error("Could not read image file: {}", imagePath);
                return new Mat();
            }
            
            // Convert BufferedImage to OpenCV Mat
            Mat mat = bufferedImageToMat(bufferedImage);
            logger.debug("Successfully loaded image: {} ({}x{})", 
                        imagePath, mat.cols(), mat.rows());
            
            return mat;
            
        } catch (IOException e) {
            logger.error("Error loading image {}: {}", imagePath, e.getMessage());
            return new Mat();
        }
    }
    
    /**
     * Converts a BufferedImage to OpenCV Mat format.
     * 
     * @param bufferedImage The BufferedImage to convert
     * @return Mat object containing the image data
     */
    private Mat bufferedImageToMat(BufferedImage bufferedImage) {
        // Convert to grayscale if the image is color
        BufferedImage grayImage;
        if (bufferedImage.getType() != BufferedImage.TYPE_BYTE_GRAY) {
            grayImage = new BufferedImage(bufferedImage.getWidth(), bufferedImage.getHeight(), 
                                        BufferedImage.TYPE_BYTE_GRAY);
            grayImage.getGraphics().drawImage(bufferedImage, 0, 0, null);
        } else {
            grayImage = bufferedImage;
        }
        
        // Get the image data as byte array
        byte[] imageData = ((DataBufferByte) grayImage.getRaster().getDataBuffer()).getData();
        
        // Create OpenCV Mat
        Mat mat = new Mat(grayImage.getHeight(), grayImage.getWidth(), CvType.CV_8UC1);
        mat.put(0, 0, imageData);
        
        return mat;
    }
    
    /**
     * Checks if the given file path represents a supported image format.
     * 
     * @param filePath Path to check
     * @return true if the file extension is supported
     */
    public boolean isSupportedFormat(String filePath) {
        String lowerPath = filePath.toLowerCase();
        return lowerPath.endsWith(".tiff") || 
               lowerPath.endsWith(".tif") ||
               lowerPath.endsWith(".png") ||
               lowerPath.endsWith(".jpg") ||
               lowerPath.endsWith(".jpeg") ||
               lowerPath.endsWith(".bmp");
    }
}
