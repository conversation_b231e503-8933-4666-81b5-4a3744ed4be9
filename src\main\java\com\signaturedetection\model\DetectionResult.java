package com.signaturedetection.model;

import java.awt.Rectangle;
import java.util.List;

/**
 * Represents the result of signature/initial detection in an image.
 */
public class DetectionResult {
    private final List<Rectangle> signatureBounds;
    private final List<Rectangle> initialBounds;
    private final double confidence;
    private final String imagePath;

    public DetectionResult(List<Rectangle> signatureBounds, List<Rectangle> initialBounds, 
                          double confidence, String imagePath) {
        this.signatureBounds = signatureBounds;
        this.initialBounds = initialBounds;
        this.confidence = confidence;
        this.imagePath = imagePath;
    }

    public List<Rectangle> getSignatureBounds() {
        return signatureBounds;
    }

    public List<Rectangle> getInitialBounds() {
        return initialBounds;
    }

    public double getConfidence() {
        return confidence;
    }

    public String getImagePath() {
        return imagePath;
    }

    public boolean hasSignatures() {
        return signatureBounds != null && !signatureBounds.isEmpty();
    }

    public boolean hasInitials() {
        return initialBounds != null && !initialBounds.isEmpty();
    }

    @Override
    public String toString() {
        return String.format("DetectionResult{signatures=%d, initials=%d, confidence=%.2f, image='%s'}", 
                           signatureBounds != null ? signatureBounds.size() : 0,
                           initialBounds != null ? initialBounds.size() : 0,
                           confidence, imagePath);
    }
}
