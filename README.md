# Signature and Initial Detection

A Java Maven project for extracting initials and signatures from TIFF images with automatic line removal preprocessing.

## Features

- **Line Removal**: Automatically detects and removes horizontal and vertical lines from images before signature extraction
- **Signature Detection**: Uses computer vision techniques to identify signature-like patterns
- **Initial Detection**: Detects smaller initial-like patterns separate from full signatures
- **TIFF Support**: Optimized for TIFF image format with support for other common formats
- **Batch Processing**: Can process individual files or entire directories
- **Configurable**: Adjustable parameters for different image types and requirements

## Requirements

- Java 11 or higher
- Maven 3.6 or higher

## Building the Project

```bash
mvn clean compile
```

## Running Tests

```bash
mvn test
```

## Building the Executable JAR

```bash
mvn clean package
```

This creates an executable JAR file in the `target/` directory.

## Usage

### Command Line Interface

```bash
java -jar target/signature-initial-detection-1.0.0.jar <input_path> [output_path]
```

**Arguments:**
- `input_path`: Path to a TIFF image file or directory containing images
- `output_path`: Optional path for output results (currently prints to console)

**Examples:**

Process a single TIFF file:
```bash
java -jar target/signature-initial-detection-1.0.0.jar document.tiff
```

Process all images in a directory:
```bash
java -jar target/signature-initial-detection-1.0.0.jar /path/to/images/
```

### Programmatic Usage

```java
import com.signaturedetection.SignatureDetectionApp;

SignatureDetectionApp app = new SignatureDetectionApp();
app.processInput("path/to/image.tiff", null);
```

## Algorithm Overview

### 1. Image Preprocessing
- **Noise Reduction**: Applies Gaussian blur and median filtering
- **Line Detection**: Uses morphological operations to detect horizontal and vertical lines
- **Line Removal**: Removes detected lines while preserving signature content
- **Binarization**: Converts to black and white using adaptive thresholding

### 2. Signature Detection
- **Contour Analysis**: Finds connected components in the processed image
- **Size Filtering**: Filters contours based on configurable size constraints
- **Aspect Ratio Analysis**: Distinguishes signatures (wider) from initials (more square)
- **Shape Complexity**: Analyzes fill ratio to identify signature-like patterns

### 3. Classification
- **Signatures**: Typically 50-400px wide, 20-200px tall, aspect ratio 0.2-0.8
- **Initials**: Typically 20-100px wide/tall, aspect ratio 0.3-2.0, higher fill ratio

## Configuration

The detection parameters can be adjusted programmatically:

```java
ContourBasedSignatureDetector detector = new ContourBasedSignatureDetector();
detector.setConfidenceThreshold(0.7);
detector.setMinimumSize(40, 15);
detector.setMaximumSignatureSize(500, 250);
```

## Output Format

The application outputs detection results to the console:

```
=== Detection Results ===
Image: document.tiff
Confidence: 0.85
Signatures found:
  Signature 1: x=120, y=450, width=180, height=45
Initials found:
  Initial 1: x=300, y=100, width=35, height=40
```

## Project Structure

```
src/
├── main/java/com/signaturedetection/
│   ├── SignatureDetectionApp.java          # Main application class
│   ├── detection/
│   │   ├── SignatureDetector.java          # Detection interface
│   │   └── ContourBasedSignatureDetector.java # Contour-based implementation
│   ├── preprocessing/
│   │   ├── ImagePreprocessor.java          # Preprocessing interface
│   │   └── LineRemovalPreprocessor.java    # Line removal implementation
│   ├── model/
│   │   └── DetectionResult.java            # Result data model
│   └── util/
│       └── ImageLoader.java                # Image loading utilities
└── test/java/com/signaturedetection/       # Unit tests
```

## Dependencies

- **OpenCV**: Computer vision operations
- **Apache Commons Imaging**: TIFF image support
- **TwelveMonkeys ImageIO**: Additional image format support
- **SLF4J + Logback**: Logging
- **JUnit 5**: Testing framework

## Troubleshooting

### OpenCV Loading Issues
If you encounter OpenCV loading errors, ensure the native libraries are properly installed:
```bash
# The application uses the OpenPNP OpenCV distribution which should work out of the box
```

### Memory Issues with Large Images
For very large TIFF files, increase JVM heap size:
```bash
java -Xmx4g -jar signature-initial-detection-1.0.0.jar large_document.tiff
```

### Poor Detection Results
- Adjust confidence threshold for stricter/looser detection
- Modify size constraints based on your document types
- Check image quality and resolution
- Ensure lines are properly removed in preprocessing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass: `mvn test`
5. Submit a pull request

## License

This project is licensed under the MIT License.
