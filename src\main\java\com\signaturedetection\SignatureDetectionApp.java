package com.signaturedetection;

import com.signaturedetection.detection.ContourBasedSignatureDetector;
import com.signaturedetection.detection.SignatureDetector;
import com.signaturedetection.model.DetectionResult;
import com.signaturedetection.preprocessing.ImagePreprocessor;
import com.signaturedetection.preprocessing.LineRemovalPreprocessor;
import com.signaturedetection.util.ImageLoader;
import nu.pattern.OpenCV;
import org.opencv.core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Rectangle;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Main application class for signature and initial detection from TIFF images.
 */
public class SignatureDetectionApp {
    
    private static final Logger logger = LoggerFactory.getLogger(SignatureDetectionApp.class);
    
    private final ImagePreprocessor preprocessor;
    private final SignatureDetector detector;
    private final ImageLoader imageLoader;
    
    public SignatureDetectionApp() {
        this.preprocessor = new LineRemovalPreprocessor();
        this.detector = new ContourBasedSignatureDetector();
        this.imageLoader = new ImageLoader();
    }
    
    public static void main(String[] args) {
        // Load OpenCV native library
        try {
            // Try loadShared first, fall back to loadLocally for Java >= 12
            try {
                OpenCV.loadShared();
                logger.info("OpenCV loaded successfully using loadShared()");
            } catch (UnsatisfiedLinkError | RuntimeException e) {
                logger.info("loadShared() not supported, falling back to loadLocally()");
                OpenCV.loadLocally();
                logger.info("OpenCV loaded successfully using loadLocally()");
            }
        } catch (Exception e) {
            logger.error("Failed to load OpenCV: {}", e.getMessage());
            System.exit(1);
        }
        
        SignatureDetectionApp app = new SignatureDetectionApp();
        
        if (args.length == 0) {
            app.printUsage();
            return;
        }
        
        String inputPath = "C:\\InfoInputSolution\\batches\\b4b9fab0-b25b-11ef-80a5-64d69a1832f2\\26274\\14.tif";
        String outputPath = "./output";
        
        try {
            app.processInput(inputPath, outputPath);
        } catch (Exception e) {
            logger.error("Error processing input: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
    
    public void processInput(String inputPath, String outputPath) throws Exception {
        Path path = Paths.get(inputPath);
        
        if (!Files.exists(path)) {
            throw new IllegalArgumentException("Input path does not exist: " + inputPath);
        }
        
        if (Files.isDirectory(path)) {
            processDirectory(path, outputPath);
        } else {
            processFile(path, outputPath);
        }
    }
    
    private void processDirectory(Path directory, String outputPath) throws Exception {
        logger.info("Processing directory: {}", directory);
        
        List<String> supportedExtensions = Arrays.asList(".tiff", ".tif", ".png", ".jpg", ".jpeg");
        
        Files.walk(directory)
            .filter(Files::isRegularFile)
            .filter(file -> supportedExtensions.stream()
                .anyMatch(ext -> file.toString().toLowerCase().endsWith(ext)))
            .forEach(file -> {
                try {
                    processFile(file, outputPath);
                } catch (Exception e) {
                    logger.error("Error processing file {}: {}", file, e.getMessage());
                }
            });
    }
    
    private void processFile(Path filePath, String outputPath) throws Exception {
        logger.info("Processing file: {}", filePath);
        
        // Load the image
        Mat image = imageLoader.loadImage(filePath.toString());
        if (image.empty()) {
            logger.warn("Could not load image: {}", filePath);
            return;
        }
        
        // Preprocess the image (remove lines, denoise, etc.)
        Mat preprocessedImage = preprocessor.preprocess(image);
        
        // Detect signatures and initials
        DetectionResult result = detector.detect(preprocessedImage, filePath.toString());
        
        // Output results
        outputResults(result, outputPath);
        
        // Clean up
        image.release();
        preprocessedImage.release();
    }
    
    private void outputResults(DetectionResult result, String outputPath) {
        logger.info("Detection results for {}: {}", result.getImagePath(), result);
        
        // Print to console
        System.out.println("=== Detection Results ===");
        System.out.println("Image: " + result.getImagePath());
        System.out.println("Confidence: " + String.format("%.2f", result.getConfidence()));
        
        if (result.hasSignatures()) {
            System.out.println("Signatures found:");
            for (int i = 0; i < result.getSignatureBounds().size(); i++) {
                Rectangle rect = result.getSignatureBounds().get(i);
                System.out.printf("  Signature %d: x=%d, y=%d, width=%d, height=%d%n", 
                                i + 1, rect.x, rect.y, rect.width, rect.height);
            }
        } else {
            System.out.println("No signatures found.");
        }
        
        if (result.hasInitials()) {
            System.out.println("Initials found:");
            for (int i = 0; i < result.getInitialBounds().size(); i++) {
                Rectangle rect = result.getInitialBounds().get(i);
                System.out.printf("  Initial %d: x=%d, y=%d, width=%d, height=%d%n", 
                                i + 1, rect.x, rect.y, rect.width, rect.height);
            }
        } else {
            System.out.println("No initials found.");
        }
        
        System.out.println();
        
        // TODO: Implement file output if outputPath is provided
        if (outputPath != null) {
            // Could save results to JSON, CSV, or annotated images
            logger.info("Output path specified but not yet implemented: {}", outputPath);
        }
    }
    
    private void printUsage() {
        System.out.println("Usage: java -jar signature-detection.jar <input_path> [output_path]");
        System.out.println();
        System.out.println("Arguments:");
        System.out.println("  input_path   Path to TIFF image file or directory containing images");
        System.out.println("  output_path  Optional path for output results (not yet implemented)");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar signature-detection.jar document.tiff");
        System.out.println("  java -jar signature-detection.jar /path/to/images/");
    }
    
    // Getters for testing
    public ImagePreprocessor getPreprocessor() {
        return preprocessor;
    }
    
    public SignatureDetector getDetector() {
        return detector;
    }
}
