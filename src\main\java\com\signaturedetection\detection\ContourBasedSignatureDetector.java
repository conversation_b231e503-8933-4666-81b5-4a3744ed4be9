package com.signaturedetection.detection;

import com.signaturedetection.model.DetectionResult;
import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Rectangle;
import java.util.ArrayList;
import java.util.List;

/**
 * Signature detector implementation using contour analysis.
 */
public class ContourBasedSignatureDetector implements SignatureDetector {
    
    private static final Logger logger = LoggerFactory.getLogger(ContourBasedSignatureDetector.class);
    
    // Configuration parameters
    private double confidenceThreshold = 0.5;
    private int minSignatureWidth = 50;
    private int minSignatureHeight = 20;
    private int maxSignatureWidth = 400;
    private int maxSignatureHeight = 200;
    private int minInitialWidth = 20;
    private int minInitialHeight = 20;
    private int maxInitialWidth = 100;
    private int maxInitialHeight = 100;
    private double aspectRatioThreshold = 0.2; // Minimum height/width ratio
    
    @Override
    public DetectionResult detect(Mat preprocessedImage, String imagePath) {
        logger.info("Starting signature detection for image: {}", imagePath);
        
        // Find contours in the preprocessed image
        List<MatOfPoint> contours = findContours(preprocessedImage);
        
        // Analyze contours to identify signatures and initials
        List<Rectangle> signatures = new ArrayList<>();
        List<Rectangle> initials = new ArrayList<>();
        
        for (MatOfPoint contour : contours) {
            Rect boundingRect = Imgproc.boundingRect(contour);
            Rectangle rect = new Rectangle(boundingRect.x, boundingRect.y, 
                                         boundingRect.width, boundingRect.height);
            
            if (isSignature(rect, contour)) {
                signatures.add(rect);
                logger.debug("Found signature at: {}", rect);
            } else if (isInitial(rect, contour)) {
                initials.add(rect);
                logger.debug("Found initial at: {}", rect);
            }
        }
        
        // Calculate overall confidence based on detection quality
        double confidence = calculateConfidence(signatures, initials, contours);
        
        logger.info("Detection completed. Found {} signatures and {} initials with confidence {}", 
                   signatures.size(), initials.size(), confidence);
        
        return new DetectionResult(signatures, initials, confidence, imagePath);
    }
    
    private List<MatOfPoint> findContours(Mat image) {
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        
        // Find contours using external retrieval mode
        Imgproc.findContours(image, contours, hierarchy, 
                           Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
        
        logger.debug("Found {} contours", contours.size());
        return contours;
    }
    
    private boolean isSignature(Rectangle rect, MatOfPoint contour) {
        // Check size constraints
        if (rect.width < minSignatureWidth || rect.width > maxSignatureWidth ||
            rect.height < minSignatureHeight || rect.height > maxSignatureHeight) {
            return false;
        }
        
        // Check aspect ratio (signatures are typically wider than they are tall)
        double aspectRatio = (double) rect.height / rect.width;
        if (aspectRatio < aspectRatioThreshold || aspectRatio > 0.8) {
            return false;
        }
        
        // Check contour area vs bounding rectangle area (signatures have complex shapes)
        double contourArea = Imgproc.contourArea(contour);
        double rectArea = rect.width * rect.height;
        double fillRatio = contourArea / rectArea;
        
        // Signatures typically have a fill ratio between 0.1 and 0.6
        return fillRatio >= 0.1 && fillRatio <= 0.6;
    }
    
    private boolean isInitial(Rectangle rect, MatOfPoint contour) {
        // Check size constraints for initials (smaller than signatures)
        if (rect.width < minInitialWidth || rect.width > maxInitialWidth ||
            rect.height < minInitialHeight || rect.height > maxInitialHeight) {
            return false;
        }
        
        // Initials can be more square-like
        double aspectRatio = (double) rect.height / rect.width;
        if (aspectRatio < 0.3 || aspectRatio > 2.0) {
            return false;
        }
        
        // Check contour complexity (initials are typically simpler)
        double contourArea = Imgproc.contourArea(contour);
        double rectArea = rect.width * rect.height;
        double fillRatio = contourArea / rectArea;
        
        // Initials typically have a higher fill ratio than signatures
        return fillRatio >= 0.2 && fillRatio <= 0.8;
    }
    
    private double calculateConfidence(List<Rectangle> signatures, List<Rectangle> initials, 
                                     List<MatOfPoint> allContours) {
        if (signatures.isEmpty() && initials.isEmpty()) {
            return 0.0;
        }
        
        int totalDetections = signatures.size() + initials.size();
        int totalContours = allContours.size();
        
        // Base confidence on the ratio of valid detections to total contours
        double baseConfidence = Math.min(1.0, (double) totalDetections / Math.max(1, totalContours));
        
        // Boost confidence if we found both signatures and initials
        if (!signatures.isEmpty() && !initials.isEmpty()) {
            baseConfidence = Math.min(1.0, baseConfidence * 1.2);
        }
        
        return Math.max(baseConfidence, confidenceThreshold);
    }
    
    @Override
    public void setConfidenceThreshold(double threshold) {
        this.confidenceThreshold = Math.max(0.0, Math.min(1.0, threshold));
    }
    
    @Override
    public void setMinimumSize(int minWidth, int minHeight) {
        this.minSignatureWidth = minWidth;
        this.minSignatureHeight = minHeight;
        this.minInitialWidth = Math.min(minWidth, this.minInitialWidth);
        this.minInitialHeight = Math.min(minHeight, this.minInitialHeight);
    }
    
    // Additional configuration methods
    public void setMaximumSignatureSize(int maxWidth, int maxHeight) {
        this.maxSignatureWidth = maxWidth;
        this.maxSignatureHeight = maxHeight;
    }
    
    public void setMaximumInitialSize(int maxWidth, int maxHeight) {
        this.maxInitialWidth = maxWidth;
        this.maxInitialHeight = maxHeight;
    }
}
