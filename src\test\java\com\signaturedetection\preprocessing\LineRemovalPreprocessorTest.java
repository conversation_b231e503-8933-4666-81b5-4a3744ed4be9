package com.signaturedetection.preprocessing;

import nu.pattern.OpenCV;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.opencv.core.Scalar;

import static org.junit.jupiter.api.Assertions.*;

class LineRemovalPreprocessorTest {
    
    private LineRemovalPreprocessor preprocessor;
    
    @BeforeAll
    static void loadOpenCV() {
        try {
            OpenCV.loadShared();
        } catch (UnsatisfiedLinkError | RuntimeException e) {
            OpenCV.loadLocally();
        }
    }
    
    @BeforeEach
    void setUp() {
        preprocessor = new LineRemovalPreprocessor();
    }
    
    @Test
    void testPreprocessWithValidImage() {
        // Create a test image (100x100 grayscale)
        Mat testImage = Mat.zeros(100, 100, CvType.CV_8UC1);
        
        // Add some content to the image
        testImage.setTo(new Scalar(255), testImage); // White background
        
        Mat result = preprocessor.preprocess(testImage);
        
        assertNotNull(result);
        assertFalse(result.empty());
        assertEquals(testImage.rows(), result.rows());
        assertEquals(testImage.cols(), result.cols());
        
        testImage.release();
        result.release();
    }
    
    @Test
    void testRemoveLines() {
        // Create a test image with horizontal and vertical lines
        Mat testImage = Mat.zeros(100, 100, CvType.CV_8UC1);
        
        // Draw horizontal line
        for (int col = 0; col < testImage.cols(); col++) {
            testImage.put(50, col, 255);
        }
        
        // Draw vertical line
        for (int row = 0; row < testImage.rows(); row++) {
            testImage.put(row, 50, 255);
        }
        
        Mat result = preprocessor.removeLines(testImage);
        
        assertNotNull(result);
        assertFalse(result.empty());
        
        // The lines should be significantly reduced
        double[] originalPixel = testImage.get(50, 50);
        double[] resultPixel = result.get(50, 50);
        
        assertTrue(resultPixel[0] < originalPixel[0], "Line removal should reduce pixel intensity");
        
        testImage.release();
        result.release();
    }
    
    @Test
    void testDenoise() {
        // Create a noisy test image
        Mat testImage = Mat.zeros(50, 50, CvType.CV_8UC1);
        testImage.setTo(new Scalar(128)); // Gray background
        
        // Add some noise
        testImage.put(10, 10, 255);
        testImage.put(10, 11, 0);
        testImage.put(11, 10, 255);
        
        Mat result = preprocessor.denoise(testImage);
        
        assertNotNull(result);
        assertFalse(result.empty());
        assertEquals(testImage.rows(), result.rows());
        assertEquals(testImage.cols(), result.cols());
        
        testImage.release();
        result.release();
    }
    
    @Test
    void testBinarize() {
        // Create a grayscale test image
        Mat testImage = Mat.zeros(50, 50, CvType.CV_8UC1);
        
        // Add different gray levels
        for (int i = 0; i < 25; i++) {
            for (int j = 0; j < 50; j++) {
                testImage.put(i, j, 100); // Dark gray
            }
        }
        for (int i = 25; i < 50; i++) {
            for (int j = 0; j < 50; j++) {
                testImage.put(i, j, 200); // Light gray
            }
        }
        
        Mat result = preprocessor.binarize(testImage);
        
        assertNotNull(result);
        assertFalse(result.empty());
        
        // Check that result is binary (only 0 or 255 values)
        double[] pixel1 = result.get(10, 10);
        double[] pixel2 = result.get(40, 40);
        
        assertTrue(pixel1[0] == 0 || pixel1[0] == 255, "Pixel should be binary");
        assertTrue(pixel2[0] == 0 || pixel2[0] == 255, "Pixel should be binary");
        
        testImage.release();
        result.release();
    }
    
    @Test
    void testConfigurationMethods() {
        preprocessor.setHorizontalKernelSize(50);
        preprocessor.setVerticalKernelSize(60);
        preprocessor.setMorphologyIterations(3);
        preprocessor.setBinaryThreshold(100.0);
        
        // Test that configuration doesn't break preprocessing
        Mat testImage = Mat.zeros(100, 100, CvType.CV_8UC1);
        testImage.setTo(new Scalar(128));
        
        Mat result = preprocessor.preprocess(testImage);
        
        assertNotNull(result);
        assertFalse(result.empty());
        
        testImage.release();
        result.release();
    }
}
