<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.864" tests="7" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\test-classes;C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\classes;C:\Users\<USER>\.m2\repository\org\openpnp\opencv\4.9.0-0\opencv-4.9.0-0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-imaging\1.0.0-alpha5\commons-imaging-1.0.0-alpha5.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-tiff\3.9.4\imageio-tiff-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-core\3.9.4\imageio-core-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-metadata\3.9.4\imageio-metadata-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-lang\3.9.4\common-lang-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-io\3.9.4\common-io-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-image\3.9.4\common-image-3.9.4.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.0\junit-jupiter-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Europe/London"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="GB"/>
    <property name="sun.boot.library.path" value="C:\InfoInputSolution\java\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972\surefirebooter-20250818161228215_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972 2025-08-18T16-12-27_962-jvmRun1 surefire-20250818161228215_1tmp surefire_0-20250818161228215_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\test-classes;C:\Users\<USER>\Documents\augment-projects\SigandIntDetection\target\classes;C:\Users\<USER>\.m2\repository\org\openpnp\opencv\4.9.0-0\opencv-4.9.0-0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-imaging\1.0.0-alpha5\commons-imaging-1.0.0-alpha5.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-tiff\3.9.4\imageio-tiff-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-core\3.9.4\imageio-core-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-metadata\3.9.4\imageio-metadata-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-lang\3.9.4\common-lang-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-io\3.9.4\common-io-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-image\3.9.4\common-image-3.9.4.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.0\junit-jupiter-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\InfoInputSolution\java"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire9183839755347986972\surefirebooter-20250818161228215_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.9+9"/>
    <property name="user.name" value="10187506"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-17.0.9+9"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.9"/>
    <property name="user.dir" value="C:\Users\<USER>\Documents\augment-projects\SigandIntDetection"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\InfoInputSolution\java\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\InfoInputSolution\java\bin;C:\Program Files\nodejs\;C:\Mavern\mvn\bin\;C:\Program Files\RedHat\Podman\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2024.3\bin;;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.1.1\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="17.0.9+9"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testSetConfidenceThreshold" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.181">
    <system-out><![CDATA[16:12:29,566 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.4.11
16:12:29,567 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - No custom configurators were discovered as a service.
16:12:29,568 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
16:12:29,568 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
16:12:29,578 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
16:12:29,578 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
16:12:29,583 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 10 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
16:12:29,583 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
16:12:29,584 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
16:12:29,584 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.xml]
16:12:29,584 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback.xml] at [file:/C:/Users/<USER>/Documents/augment-projects/SigandIntDetection/target/classes/logback.xml]
16:12:29,654 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
16:12:29,654 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
16:12:29,659 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:12:29,679 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [FILE]
16:12:29,679 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
16:12:29,685 |-WARN in ch.qos.logback.core.model.processor.ImplicitModelHandler - Ignoring unknown property [maxFileSize] in [ch.qos.logback.core.rolling.TimeBasedRollingPolicy]
16:12:29,687 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@464400749 - setting totalSizeCap to 300 MB
16:12:29,693 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@464400749 - No compression will be used
16:12:29,694 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@464400749 - Will use the pattern logs/signature-detection.%d{yyyy-MM-dd}.%i.log for the active file
16:12:29,698 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd' from file name pattern 'logs/signature-detection.%d{yyyy-MM-dd}.%i.log'.
16:12:29,698 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at midnight.
16:12:29,698 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to 2025-08-18T15:12:29.698Z
16:12:29,700 |-ERROR in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Filename pattern [logs/signature-detection.%d{yyyy-MM-dd}.%i.log] contains an integer token converter, i.e. %i, INCOMPATIBLE with this configuration. Remove it.
16:12:29,700 |-WARN in c.q.l.core.rolling.TimeBasedRollingPolicy@464400749 - Subcomponent did not start. TimeBasedRollingPolicy will not start.
16:12:29,700 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:12:29,700 |-WARN in ch.qos.logback.core.rolling.RollingFileAppender[FILE] - TriggeringPolicy has not started. RollingFileAppender will not start
16:12:29,701 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [com.signaturedetection] to INFO
16:12:29,702 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [com.signaturedetection] to false
16:12:29,702 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[com.signaturedetection]
16:12:29,703 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[com.signaturedetection]
16:12:29,703 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to WARN
16:12:29,703 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
16:12:29,703 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@5026735c - End of configuration.
16:12:29,704 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@1b45c0e - Registering current configuration as safe fallback point
16:12:29,704 |-INFO in ch.qos.logback.classic.util.ContextInitializer@2ce6c6ec - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 120 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

16:12:29.711 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: test.tiff
16:12:29.717 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
    <system-err><![CDATA[Aug 18, 2025 4:12:28 PM nu.pattern.OpenCV$SharedLoader <init>
SEVERE: OpenCV.loadShared() is not supported in Java >= 12. Falling back to OpenCV.loadLocally().
]]></system-err>
  </testcase>
  <testcase name="testDetectionResultProperties" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.002">
    <system-out><![CDATA[16:12:29.727 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: properties_test.tiff
16:12:29.727 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
  <testcase name="testSetMaximumSignatureSize" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.014">
    <system-out><![CDATA[16:12:29.731 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: large_test.tiff
16:12:29.742 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
  <testcase name="testDetectWithEmptyImage" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.001">
    <system-out><![CDATA[16:12:29.744 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: test.tiff
16:12:29.744 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
  <testcase name="testDetectWithInitialLikeShape" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.001">
    <system-out><![CDATA[16:12:29.745 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: initial_test.tiff
16:12:29.745 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
  <testcase name="testSetMinimumSize" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.001">
    <system-out><![CDATA[16:12:29.746 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: small_test.tiff
16:12:29.747 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
  <testcase name="testDetectWithSignatureLikeShape" classname="com.signaturedetection.detection.ContourBasedSignatureDetectorTest" time="0.001">
    <system-out><![CDATA[16:12:29.748 [main] INFO  c.s.d.ContourBasedSignatureDetector - Starting signature detection for image: signature_test.tiff
16:12:29.748 [main] INFO  c.s.d.ContourBasedSignatureDetector - Detection completed. Found 0 signatures and 0 initials with confidence 0.0
]]></system-out>
  </testcase>
</testsuite>