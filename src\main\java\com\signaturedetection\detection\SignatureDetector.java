package com.signaturedetection.detection;

import com.signaturedetection.model.DetectionResult;
import org.opencv.core.Mat;

/**
 * Interface for signature and initial detection.
 */
public interface SignatureDetector {
    
    /**
     * Detects signatures and initials in the preprocessed image.
     * 
     * @param preprocessedImage The preprocessed image matrix
     * @param imagePath The path to the original image file
     * @return DetectionResult containing found signatures and initials
     */
    DetectionResult detect(Mat preprocessedImage, String imagePath);
    
    /**
     * Sets the minimum confidence threshold for detections.
     * 
     * @param threshold The confidence threshold (0.0 to 1.0)
     */
    void setConfidenceThreshold(double threshold);
    
    /**
     * Sets the minimum size for signature detection.
     * 
     * @param minWidth Minimum width in pixels
     * @param minHeight Minimum height in pixels
     */
    void setMinimumSize(int minWidth, int minHeight);
}
